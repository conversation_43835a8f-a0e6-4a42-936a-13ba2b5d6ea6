import 'package:json_annotation/json_annotation.dart';

part 'role.g.dart';

@JsonSerializable()
class Role {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String name;

  @Json<PERSON>ey(name: 'GroupId', defaultValue: '')
  String groupId;

  /// 系统配置 1.是 2.不是
  @Json<PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  Role({this.id, this.name = '', this.groupId = '', this.defaultenum = 2});

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$RoleTo<PERSON>son(this);
}
