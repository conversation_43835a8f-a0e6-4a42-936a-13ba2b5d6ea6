import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/models/role_mgmt/group_config.dart';
import 'package:octasync_client/views/admin/role_auth_management/btn.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_mgmt_state.dart';
import 'package:provider/provider.dart';

class CreateRoleGroupDialog extends StatefulWidget {
  const CreateRoleGroupDialog({super.key});

  @override
  State<CreateRoleGroupDialog> createState() => _CreateRoleGroupDialogState();
}

class _CreateRoleGroupDialogState extends State<CreateRoleGroupDialog> {
  Dio dio = Dio();
  final String _token =
      "tCLQz+0RWr9GGU1b43xolDcdnBBZxLZFxfg9QszX6Wem9LbM34dsIe1qcLLuM1K4tjZAH86g8nvUdKkEj0Pt9d+3O3FTMatVTMKaPecZ7DXBpp/9PsfGGNHg5FqujNxLvZz/Dhrx1IBFVpo34uqBSCJnqG1091Vmc/XtLwUhsbEZvhovWl+sTzZIZy1cdjnLXHPSc4Q/QEm0DyTo3lxu1S/9C5BoZOV2vBWiwzpJtEs=";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var model = context.select<RoleMgmtState, GroupConfig>((state) => state.groupConfigModel);

    return Container(
      width: 500,
      height: 200,
      color: Colors.white,
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          //弹框标题
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                child: Selector<RoleMgmtState, ({String dialogStaus})>(
                  selector: (context, provider) => (dialogStaus: provider.dialogStatus),
                  builder: (context, data, child) {
                    return Text(data.dialogStaus);
                  },
                ),
              ),
            ],
          ),

          Expanded(
            child: AppBasicTextField(
              initialValue: model.name,
              onChanged: (value) {
                model.name = value;
              },
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  // borderSide: BorderSide(
                  //   color: Colors.grey,
                  //   width: 1,
                  // ),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 10),
                hintText: '请输入关键字',
              ),
            ),
          ),

          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              btn(
                title: '取消',
                onTap: () {
                  context.read<RoleMgmtState>().closeGroupDialog();
                },
              ),
              SizedBox(width: 10),
              btn(
                title: '确定',
                onTap: () {
                  //保存
                  handleSave(model);
                  //关闭弹框
                  context.read<RoleMgmtState>().closeGroupDialog();
                  //刷新列表
                  context.read<RoleMgmtState>().reloadGroupList();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  BaseOptions _getOption() {
    const String APPLICATION_JSON = "application/json; charset=utf-8";

    return BaseOptions(
      connectTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 10),
      headers: {'Content-Type': APPLICATION_JSON, 'Accept': APPLICATION_JSON, 'token': _token},
      validateStatus: (status) {
        // 允许所有状态码，手动处理错误
        return status != null && status < 500;
      },
    );
  }

  Future<void> handleSave(GroupConfig obj) async {
    try {
      dio.options = _getOption();
      var response = await dio.post(
        'http://192.168.99.100:7001/api/Business/Role/AddOrEditGroup',
        data: jsonEncode(obj),
      );

      if (response.statusCode == 200) {
        AppTableGeneralHelper.showMessage('保存分组————————成功');
      } else {
        AppTableGeneralHelper.showMessage('请求表格列表————————失败 ${response.statusCode}');
      }
    } catch (e) {
      print('handleSave 异常: $e');
    }
  }
}
